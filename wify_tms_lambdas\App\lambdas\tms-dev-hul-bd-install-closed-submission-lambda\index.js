const axios = require("axios");

const filesPrefixUrl = "https://tms-hul-media-demo.wify.co.in/";

const logAxiosError = (error) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.log(error.response.data);
    console.log(error.response.status);
    console.log(error.response.headers);
  } else if (error.request) {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    console.log(error.request);
  } else {
    // Something happened in setting up the request that triggered an Error
    console.log("Error", error.message);
  }
  // console.log(error.config);
};

// Salesforce API request with Bearer token
const makeRequest = async (data) => {
  const salesforceUrl =
    "https://aosmithpureitq--aosqas001.sandbox.my.salesforce.com/services/apexrest/TMSBangladeshUpdateAndCloseWorkStatus";
  const bearerToken =
    "00Ddm000005NsT3!AQEAQE2qwaRJRIUKvQlmkWpIji.oIcv9Q2UBktb35r6iijb7t4cmJLVpyB87fVkAENFbCdCc3h70dEGDpDZfVvENTQ65HuNy";

  const headers = {
    Authorization: `Bearer ${bearerToken}`,
    "Content-Type": "application/json",
    Cookie:
      "BrowserId=6cQ6rd1TEe-u_MM-5GGxMQ; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1",
  };

  return await axios.post(salesforceUrl, data, { headers });
};

const convertAttachmentToPublicUrl = (internalUrl) => {
  if (internalUrl) {
    const string = internalUrl; //"org_14/capture_image_1124406008470300_uaJ1IeWPIghOVE60.png";
    return `${filesPrefixUrl}${string}`;
  } else {
    return "";
  }
};

const handler = async (event) => {
  let responseStatus = false;
  let responseMessage = "Error please contact admin";

  console.log("event form_data", event.form_data);
  console.log("event request_data", event.request_data);

  let lineItemsMap = [
    {
      item_label: "Line Item 1",
      qty_label: "Quantity 1",
      price_label: "Price 1",
    },
    {
      item_label: "Line Item 2",
      qty_label: "Quantity 2",
      price_label: "Price 2",
    },
    {
      item_label: "Line Item 3",
      qty_label: "Quantity 3",
      price_label: "Price 3",
    },
    {
      item_label: "Line Item 4",
      qty_label: "Quantity 4",
      price_label: "Price 4",
    },
    {
      item_label: "Line Item 5",
      qty_label: "Quantity 5",
      price_label: "Price 5",
    },
  ];

  const fieldVsKey = {
    tms_case_id: "tms_display_code",
    new_title: "a0d7b8e5-e9fe-4cb7-bcaa-0702c2d84f33",
    new_description: "c159b055-77a9-48c2-9356-c08b219c0e6c",
    new_part_name: "226bcb66-b052-4eba-8764-cea318dbdfd3",
    new_action_taken: "35c4abc0-9864-4fcd-9ded-4b00c752e99d",
    new_case_category: "19dc544c-749c-4fda-a9e3-cb02c7658987",
    device_category: "333c80da-22c8-4586-ab2b-3e8eb31bf7b2",
    "Line Item 1": "134dbc2f-ac0d-4516-bfc1-ab5e3a334411",
    "Quantity 1": "f1c1b7d0-f827-4e62-bdf3-83b49df24678",
    "Price 1": "9a09dcc3-e0a0-47e0-8393-2043b5b2d38c",
    "Line Item 2": "4f94e4e0-37c9-4079-b735-a4b05cb50c36",
    "Quantity 2": "0bc8731e-a1b7-499f-8912-3b99cd6d1458",
    "Price 2": "63082de1-2e87-4815-a6e4-06170600dcb4",
    "Line Item 3": "81deecbd-d08e-46d7-9cba-1f7cecd81d50",
    "Quantity 3": "c1e40fcc-0c58-437a-88ef-93c1bc8a489c",
    "Price 3": "45687217-5561-4ca6-9a98-4337869fafb2",
    "Line Item 4": "36c70bd8-b2cf-49cc-be64-63010a40c208",
    "Quantity 4": "760e9518-3fea-48ff-85e8-c35cc7751edd",
    "Price 4": "c7518a6d-a6a5-42e3-b9b3-4d2a0abc850c",
    "Line Item 5": "0ea8613d-6992-48f8-a079-f08824694610",
    "Quantity 5": "205ff35d-1206-4370-8be7-dda7007da9de",
    "Price 5": "ac146071-d0bd-4b60-b7e3-3204a43c4cc0",
    service_type: "dcf8f086-d9b8-44b2-8772-4fad70b4dbab",
    service_charge: "9bae236a-90ed-4e3d-b62f-30fd778e2703",
    total_amount: "3bede720-e0e6-41bb-819f-d4da614e8f26",
    before_water_pressure: "4f040294-7ca5-4842-aea7-f21426ac82c3",
    after_water_pressure: "7772529a-665c-4da4-a820-53efd9df7b53",
    pump_pressure: "761c8428-767b-4864-9dca-e1fe7e2e5d33",
    rejection_flow_rate: "88860193-b96f-4065-9d9d-9899905e5d4d",
    retention_flow_rate: "316cada1-0ac0-44d3-a534-fbcf085a6c31",
    ESF_refittment: "9b0f7827-1176-4fbf-8616-ee8e62ecae27",
    input_water_TDS: "76eaf50b-d279-41df-a490-5b8d5d3dc3bb",
    output_water_TDS: "54d35c35-0d0b-4fa5-9674-3c1cdf748d06",
    hardness_input_water: "3ed7adb6-14ad-4975-a520-cfb4d2595eb0",
    iron_level_input_water: "17ee16b7-5a9f-468c-b79d-7f6d0c28e170",
    input_voltage: "da414e32-eb21-4167-9b7d-f3e16811ceff",
    output_voltage: "fa72454f-9e88-4a91-95a2-95747a782f60",
    service_invoice_img: "a46e6e2e-8c33-402c-bd77-df45dac2d485",
    after_service_img: "bb1dd73b-8977-48c2-9cfa-217f182a095d",
    device_invoice_img: "ce1f194d-e579-4e07-9ba1-6ad2b9a39b01",
  };

  const tmsCaseId = event.request_data[fieldVsKey.tms_case_id];
  const newTitle = event.form_data[fieldVsKey.new_title];
  const newDescription = event.form_data[fieldVsKey.new_description];
  const newPartName = event.form_data[fieldVsKey.new_part_name];
  const newActionTaken = event.form_data[fieldVsKey.new_action_taken];
  const newCaseCategory = event.form_data[fieldVsKey.new_case_category];
  const deviceCategory = event.request_data[fieldVsKey.device_category];
  const serviceType = event.form_data[fieldVsKey.service_type];
  const serviceCharge = event.form_data[fieldVsKey.service_charge];
  const totalAmount = event.form_data[fieldVsKey.total_amount];
  const beforeWaterPressure = event.form_data[fieldVsKey.before_water_pressure];
  const afterWaterPressure = event.form_data[fieldVsKey.after_water_pressure];
  const pumpPressure = event.form_data[fieldVsKey.pump_pressure];
  const rejectionFlowRate = event.form_data[fieldVsKey.rejection_flow_rate];
  const retentionFlowRate = event.form_data[fieldVsKey.retention_flow_rate];
  const ESFRefittment = event.form_data[fieldVsKey.ESF_refittment];
  const inputWaterTDS = event.form_data[fieldVsKey.input_water_TDS];
  const outputWaterTDS = event.form_data[fieldVsKey.output_water_TDS];
  const hardnessInputWater = event.form_data[fieldVsKey.hardness_input_water];
  const ironLevelInputWater =
    event.form_data[fieldVsKey.iron_level_input_water];
  const inputVoltage = event.form_data[fieldVsKey.input_voltage];
  const outputVoltage = event.form_data[fieldVsKey.output_voltage];
  const attachments = event.form_data.attachments;

  const serviceInvoiceImg = convertAttachmentToPublicUrl(
    event.form_data.attachments[fieldVsKey.service_invoice_img]?.[0]
  );
  const afterServiceImg = convertAttachmentToPublicUrl(
    event.form_data.attachments[fieldVsKey.after_service_img]?.[0]
  );
  const deviceInvoiceImg = convertAttachmentToPublicUrl(
    event.form_data.attachments[fieldVsKey.device_invoice_img]?.[0]
  );

  let itemDataFrNetsuite = [];
  lineItemsMap.map((singleLineItemMap) => {
    const item = event.form_data[fieldVsKey[singleLineItemMap.item_label]];
    if (item) {
      itemDataFrNetsuite.push({
        item: item,
        quantity: event.form_data[fieldVsKey[singleLineItemMap.qty_label]],
        price: event.form_data[fieldVsKey[singleLineItemMap.price_label]],
        total: totalAmount,
      });
    }
  });

  console.log("itemDataFrNetsuite", itemDataFrNetsuite);

  let data = {
    tms_case_id: tmsCaseId,
    newTitle: newTitle,
    newDescription: newDescription,
    newPartName: newPartName,
    newActionTaken: newActionTaken,
    newCaseCategory: newCaseCategory,
    device_category: deviceCategory,
    Items_Data: itemDataFrNetsuite,
    service_type: serviceType,
    service_charge: serviceCharge,
    total_amount: totalAmount,
    before_water_pressure: beforeWaterPressure,
    after_water_pressure: afterWaterPressure,
    pump_pressure: pumpPressure,
    rejection_flow_rate: rejectionFlowRate,
    retention_flow_rate: retentionFlowRate,
    ESF_refittment: ESFRefittment,
    input_water_TDS: inputWaterTDS,
    output_water_TDS: outputWaterTDS,
    hardness_input_water: hardnessInputWater,
    iron_level_input_water: ironLevelInputWater,
    service_invoice_img: serviceInvoiceImg,
    after_service_img: afterServiceImg,
    device_invoice_img: deviceInvoiceImg,
    input_voltage: inputVoltage,
    output_voltage: outputVoltage,
    attachments: attachments,
  };

  console.log("salesforce api call data", data);
  try {
    const response = await makeRequest(data);
    console.log("salesforce api response", response.data); // Handle the API response data

    const salesforceData = response.data.data?.[0] || response.data;

    if (salesforceData && salesforceData.message) {
      let { message, responseCode, status } = salesforceData;
      responseMessage = message;
      console.log(
        "message,responseCode,status : ",
        message,
        responseCode,
        status
      );
      if (status == "Success") {
        responseStatus = true; //
      } else {
        responseMessage = message;
      }
    } else {
      // this is a fatal error from salesforce
      // the response is not as per documentation rxd
      responseMessage = JSON.stringify(response.data);
    }
  } catch (error) {
    logAxiosError(error);
    // handle error
  }

  // TODO implement
  const response = {
    status: responseStatus,
    message: responseMessage,
  };
  return response;
};

// handler({});
exports.handler = handler;
