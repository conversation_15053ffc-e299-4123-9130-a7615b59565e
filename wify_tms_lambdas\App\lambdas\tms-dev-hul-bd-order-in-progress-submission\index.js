const axios = require("axios");

const filesPrefixUrl = "https://tms-hul-media-demo.wify.co.in/";

const logAxiosError = (error) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.log(error.response.data);
    console.log(error.response.status);
    console.log(error.response.headers);
  } else if (error.request) {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    console.log(error.request);
  } else {
    // Something happened in setting up the request that triggered an Error
    console.log("Error", error.message);
  }
  // console.log(error.config);
};

// Salesforce API request with Bearer token
const makeRequest = async (data) => {
  const salesforceUrl =
    "https://aosmithpureitq--aosqas001.sandbox.my.salesforce.com/services/apexrest/TMSBangladeshVerifySerialNumber";
  const bearerToken =
    "00Ddm000005NsT3!AQEAQE2qwaRJRIUKvQlmkWpIji.oIcv9Q2UBktb35r6iijb7t4cmJLVpyB87fVkAENFbCdCc3h70dEGDpDZfVvENTQ65HuNy";

  const headers = {
    Authorization: `Bearer ${bearerToken}`,
    "Content-Type": "application/json",
    Cookie:
      "BrowserId=6cQ6rd1TEe-u_MM-5GGxMQ; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1",
  };

  return await axios.post(salesforceUrl, data, { headers });
};

const convertAttachmentToPublicUrl = (internalUrl) => {
  if (internalUrl) {
    const string = internalUrl; //"org_14/capture_image_1124406008470300_uaJ1IeWPIghOVE60.png";
    return `${filesPrefixUrl}${string}`;
  } else {
    return "";
  }
};

const handler = async (event) => {
  let responseStatus = false;
  let responseMessage = "Error please contact admin";

  console.log("event form_data", event.form_data);
  console.log("event request_data", event.request_data);

  const fieldVsKey = {
    tms_order_id: "tms_display_code",
    barcode_scanner: "80e682dc-f8f1-4254-a4e3-1b16f5a06cc7",
    barcode_number: "e148602d-e178-4b02-904f-de05069be05c",
    barcode_img: "c280920e-c237-4fe4-b4c7-ab8248cecb87",
    before_service_image: "6c6234ba-6103-4ec7-9d92-f681edb2b5b5",
  };

  const tmsOrderId = event.request_data[fieldVsKey.tms_order_id];
  const autoScannedBarcode = event.form_data[fieldVsKey.barcode_scanner];
  const manualEnteredBarcode = event.form_data[fieldVsKey.barcode_number];
  const barcodeImage = convertAttachmentToPublicUrl(
    event.form_data.attachments[fieldVsKey.barcode_img]?.[0]
  );
  const beforeServiceImage = convertAttachmentToPublicUrl(
    event.form_data.attachments[fieldVsKey.before_service_image]?.[0]
  );

  console.log("tmsOrderId", tmsOrderId);
  console.log("autoScannedBarcode", autoScannedBarcode);
  console.log("manualEnteredBarcode", manualEnteredBarcode);
  console.log("beforeServiceImage", beforeServiceImage);
  console.log("barcodeImage", barcodeImage);

  let data = {
    tms_order_id: tmsOrderId,
    barcode_number: autoScannedBarcode || manualEnteredBarcode,
    before_service_image: beforeServiceImage,
    barcode_img: barcodeImage,
    barcode_scanner: "",
  };

  console.log("salesforce api call data", data);
  try {
    const response = await makeRequest(data);
    console.log("salesforce api response", response.data); // Handle the API response data

    const salesforceData = response.data.data?.[0] || response.data;

    if (salesforceData && salesforceData.message) {
      let { message, responseCode, status } = salesforceData;
      responseMessage = message;
      console.log(
        "message,responseCode,status : ",
        message,
        responseCode,
        status
      );
      if (status == "Success") {
        responseStatus = true; //
      } else {
        responseMessage = message;
      }
    } else {
      // this is a fatal error from salesforce
      // the response is not as per documentation rxd
      responseMessage = JSON.stringify(response.data);
    }
  } catch (error) {
    console.log("Error from sales force catch block: ", error);
    logAxiosError(error);
    // handle error
  }

  if (autoScannedBarcode == undefined && manualEnteredBarcode == undefined) {
    responseMessage =
      "You have already updated this status, you cannot update it again.";
  }

  // TODO implement
  const response = {
    status: responseStatus,
    message: responseMessage,
  };
  return response;
};

// handler({});

exports.handler = handler;
