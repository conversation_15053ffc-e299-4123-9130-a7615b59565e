const axios = require("axios");

const atmMasterToken =
  "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************.lKcjIVvrYIFjHDeQJbZnWznBVwnIJcDcMvgKt44c0nU";

const filesPrefixUrl = "https://tms-hul-media-demo.wify.co.in/";

const logAxiosError = (error) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.log(error.response.data);
    console.log(error.response.status);
    console.log(error.response.headers);
  } else if (error.request) {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    console.log(error.request);
  } else {
    // Something happened in setting up the request that triggered an Error
    console.log("Error", error.message);
  }
  // console.log(error.config);
};

// Salesforce API request with Bearer token
const makeRequest = async (data) => {
  const salesforceUrl =
    "https://aosmithpureitq--aosqas001.sandbox.my.salesforce.com/services/apexrest/deviceItemOptions";
  const bearerToken =
    "00Ddm000005NsT3!AQEAQE2qwaRJRIUKvQlmkWpIji.oIcv9Q2UBktb35r6iijb7t4cmJLVpyB87fVkAENFbCdCc3h70dEGDpDZfVvENTQ65HuNy";

  const headers = {
    Authorization: `Bearer ${bearerToken}`,
    "Content-Type": "application/json",
    Cookie:
      "BrowserId=6cQ6rd1TEe-u_MM-5GGxMQ; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1",
  };

  return await axios.post(salesforceUrl, data, { headers });
};

const modifyFieldInMeta = (meta, fieldKey, newKeyValueObj) => {
  meta.map((singleField, index) => {
    if (singleField.key == fieldKey) {
      singleField = {
        ...singleField,
        ...newKeyValueObj,
      };
      meta[index] = singleField;
    }
  });
};

const getObjByKeyFrmArray = (array = [], key, value) => {
  return array.filter((singleObj) => singleObj[key] == value)?.[0];
};

function getUniqueEntries(array, keyName) {
  const uniqueEntries = [];
  const uniqueKeys = {};

  for (let i = 0; i < array.length; i++) {
    const obj = array[i];
    const key = obj[keyName];

    if (!uniqueKeys[key]) {
      // Add entry to the uniqueEntries array
      uniqueEntries.push(obj);
      // Add key to the uniqueKeys object
      uniqueKeys[key] = true;
    }
  }

  return uniqueEntries;
}

function getFieldOptions(data, field, int_id, extra_keys = []) {
  const fieldOptions = [];
  const existingKeys = new Set();

  data.forEach((obj) => {
    if (!existingKeys.has(obj[int_id])) {
      const option = { label: obj[field], value: obj[int_id] };

      extra_keys.forEach((key) => {
        option[key] = obj[key];
      });

      fieldOptions.push(option);
      existingKeys.add(obj[int_id]);
    }
  });

  return fieldOptions;
}

function getKeyByValue(data, value) {
  return Object.keys(data).find((key) => data[key] === value);
}

function hideEverythingOnTheFormExcept(meta, exceptionLabels = []) {
  meta.map((singleField, index) => {
    singleField = {
      ...singleField,
      hide: !exceptionLabels.includes(singleField.label),
    };
    meta[index] = singleField;
  });
}

function makeFieldsVisibleAndMandatory(
  meta,
  labelToKeyMap,
  mandatoryLabels = []
) {
  mandatoryLabels.forEach((singleField) => {
    modifyFieldInMeta(meta, labelToKeyMap[singleField], {
      required: true,
      hide: false,
    });
  });
}

function hideMultipleFields(meta, labelToKeyMap, mandatoryLabels = []) {
  mandatoryLabels.forEach((singleField) => {
    modifyFieldInMeta(meta, labelToKeyMap[singleField], {
      hide: true,
    });
  });
}

async function getOptionsFromATMMasterFr(params) {
  console.log("ATM api call params", params);
  const url = "https://hul-bd-atm-dataset-service.wify.co.in/api/data_by_group";
  const headers = {
    accept: "application/json",
    Authorization: atmMasterToken,
  };
  let atmMasterResponse = await axios.get(url, { headers, params });
  console.log("ATM api response", JSON.stringify(atmMasterResponse.data));
  return atmMasterResponse.data.result;
}

function modifyLineItemFields(
  meta,
  lineItemDetails,
  labelToKeyMap,
  updateMeta
) {
  Object.keys(lineItemDetails).map((singleLabel) => {
    modifyFieldInMeta(
      meta,
      labelToKeyMap[lineItemDetails[singleLabel]],
      updateMeta
    );
  });
}

function getNumberOfItemsToShow(
  allValues,
  numberOfLineItemsOptionsMap,
  labelToKeyMap
) {
  const selectedRadio = allValues[labelToKeyMap["Number of line items"]];
  const radioOption = getObjByKeyFrmArray(
    numberOfLineItemsOptionsMap,
    "value",
    selectedRadio
  );
  return radioOption?.label;
}

function allValuesIsInitial(allValues) {
  const emptyAllValues = {
    attachments: {},
    mic_files: {},
    camera_files: {},
  };
  return JSON.stringify(allValues) == JSON.stringify(emptyAllValues);
}

const checkIfLineItemOptionsIsLoaded = (meta, key, labelToKeyMap) => {
  let lineItemOptions = getObjByKeyFrmArray(meta, key, "Line Item 1")?.options;
  console.log("line item options", lineItemOptions, meta);
  if (lineItemOptions?.length > 0) {
    const lineItemOptionisLoaded =
      lineItemOptions.length !== 2 &&
      lineItemOptions.some((item) => item.label !== "Option1") &&
      lineItemOptions.some((item) => item.label !== "Option2");
    console.log("checkIfLineItemOptionsIsisLoaded -->", lineItemOptionisLoaded);
    return lineItemOptionisLoaded;
  } else {
    return false;
  }
};

const callLineItemOptionAPI = (allValues, changedValues, labelToKeyMap) => {
  const mandatoryFields = [
    "New Title",
    "New Description",
    "New Part Name",
    "New Action Taken",
  ];
  let checkIfAnyMandatoryFieldValueChanged = false;
  let checkIfAllValuesAreFilled = true;
  let apiCallFlag = false;
  mandatoryFields.forEach((singleField) => {
    if (changedValues[labelToKeyMap[singleField]]) {
      checkIfAnyMandatoryFieldValueChanged = true;
    }
    if (!allValues[labelToKeyMap[singleField]]) {
      checkIfAllValuesAreFilled = false;
    }
  });
  if (checkIfAllValuesAreFilled && checkIfAnyMandatoryFieldValueChanged) {
    apiCallFlag = true;
  }
  return apiCallFlag;
};

const handler = async (event) => {
  let responseStatus = false;
  let responseMessage = "No response received from Netsuite 1";

  const { meta, allValues, changedValues, request_data, currentMeta } = event;

  console.log("event", event);

  const labelToKeyMap = {};
  const headingsKeyMap = {};

  meta.map((singleField) => {
    labelToKeyMap[singleField.label] = singleField.key;
  });

  meta.map((singleField) => {
    if (singleField.cust_component == "legend") {
      headingsKeyMap[singleField.cust_component_value] = singleField.key;
    }
  });

  console.log("meta", JSON.stringify(event.meta));
  console.log("event request_data", event.request_data);

  const tms_order_id = request_data["tms_display_code"];

  let lineItemsMap = [
    {
      item_label: "Line Item 1",
      is_claimable: "Is claimable 1",
      qty_label: "Quantity 1",
      price_label: "Price 1",
    },
    {
      item_label: "Line Item 2",
      is_claimable: "Is claimable 2",
      qty_label: "Quantity 2",
      price_label: "Price 2",
    },
    {
      item_label: "Line Item 3",
      is_claimable: "Is claimable 3",
      qty_label: "Quantity 3",
      price_label: "Price 3",
    },
    {
      item_label: "Line Item 4",
      is_claimable: "Is claimable 4",
      qty_label: "Quantity 4",
      price_label: "Price 4",
    },
    {
      item_label: "Line Item 5",
      is_claimable: "Is claimable 5",
      qty_label: "Quantity 5",
      price_label: "Price 5",
    },
  ];
  let lineItemOptions = [];
  let isFirstTime =
    Object.keys(changedValues).length == 0 && allValuesIsInitial(allValues);

  const manipulatedFieldValues = {};

  //to be decided from service req meta
  const fieldVsKey = {
    refItemCategoryId: "253e7550-9162-4704-9b6b-14a863f6aa16",
    refItemCategory: "333c80da-22c8-4586-ab2b-3e8eb31bf7b2",
    serviceCharge: "ea29dbfa-091f-42d3-81e9-66e328a2b9a4",
    serviceType: "db6a3bcc-48e3-41b6-a74b-4bb9dd641eba",
  };

  const serviceCharge = request_data[fieldVsKey.serviceCharge];

  const serviceType = request_data[fieldVsKey.serviceType];

  const refItemCategoryId = request_data[fieldVsKey.refItemCategoryId];

  const refItemCategory = request_data[fieldVsKey.refItemCategory];

  let lineItemOptionIsLoaded = checkIfLineItemOptionsIsLoaded(
    currentMeta,
    "label",
    labelToKeyMap
  );

  let titleOptions = [];

  // check if the API based data is already there
  if (isFirstTime) {
    // Get the data from API

    // just show title
    hideEverythingOnTheFormExcept(meta, ["New Title"]);
    // get options for title using device category id from service request
    let atmResponseFrTitle = await getOptionsFromATMMasterFr({
      group_by: JSON.stringify([
        "ref_item_category_id",
        "ref_item_category",
        "title_id",
        "title",
      ]),
      where_clause: JSON.stringify({
        ref_item_category_id: refItemCategoryId,
      }),
    });
    if (atmResponseFrTitle) {
      titleOptions = getFieldOptions(atmResponseFrTitle, "title", "title_id");
    }
  } else {
    console.log("running else");

    titleOptions = [
      ...getObjByKeyFrmArray(currentMeta, "key", labelToKeyMap["New Title"])
        ?.options,
    ];

    //get options for respective fields and modify the form meta

    let descriptionOptions = [
      ...getObjByKeyFrmArray(
        currentMeta,
        "key",
        labelToKeyMap["New Description"]
      )?.options,
    ];
    modifyFieldInMeta(meta, labelToKeyMap["New Description"], {
      options: descriptionOptions,
    });

    let partNameOptions = [
      ...getObjByKeyFrmArray(currentMeta, "key", labelToKeyMap["New Part Name"])
        ?.options,
    ];
    modifyFieldInMeta(meta, labelToKeyMap["New Part Name"], {
      options: partNameOptions,
    });

    let actionTakenOptions = [
      ...getObjByKeyFrmArray(
        currentMeta,
        "key",
        labelToKeyMap["New Action Taken"]
      )?.options,
    ];
    modifyFieldInMeta(meta, labelToKeyMap["New Action Taken"], {
      options: actionTakenOptions,
    });

    // see if title is changed
    if (changedValues[labelToKeyMap["New Title"]]) {
      // reset description, partename and action taken
      ["New Description", "New Part Name", "New Action Taken"].map(
        (singelFieldLabel) => {
          manipulatedFieldValues[labelToKeyMap[singelFieldLabel]] = "";
        }
      );
      // hide everything after description
      hideEverythingOnTheFormExcept(meta, ["New Title", "New Description"]);
      let descriptionOptions = [];
      let atmResponseFrDescription = await getOptionsFromATMMasterFr({
        group_by: JSON.stringify([
          "ref_item_category_id",
          "ref_item_category",
          "title_id",
          "title",
          "description_id",
          "description",
        ]),
        where_clause: JSON.stringify({
          ref_item_category_id: refItemCategoryId,
          title_id: allValues[labelToKeyMap["New Title"]],
        }),
      });

      if (atmResponseFrDescription) {
        descriptionOptions = getFieldOptions(
          atmResponseFrDescription,
          "description",
          "description_id"
        );
      }
      modifyFieldInMeta(meta, labelToKeyMap["New Description"], {
        options: descriptionOptions,
      });
    }

    // see if description is changed
    if (changedValues[labelToKeyMap["New Description"]]) {
      // reset description, partename and action taken
      ["New Part name", "New Action Taken"].map((singelFieldLabel) => {
        manipulatedFieldValues[labelToKeyMap[singelFieldLabel]] = "";
      });
      // hide everything after description
      hideEverythingOnTheFormExcept(meta, [
        "New Title",
        "New Description",
        "New Part Name",
      ]);

      let atmResponseFrPartName = await getOptionsFromATMMasterFr({
        group_by: JSON.stringify([
          "ref_item_category_id",
          "ref_item_category",
          "title_id",
          "title",
          "description_id",
          "description",
          "partName_id",
          "partName",
        ]),
        where_clause: JSON.stringify({
          ref_item_category_id: refItemCategoryId,
          title_id: allValues[labelToKeyMap["New Title"]],
          description_id: allValues[labelToKeyMap["New Description"]],
        }),
      });
      let partNameOptions = [];
      if (atmResponseFrPartName) {
        partNameOptions = getFieldOptions(
          atmResponseFrPartName,
          "partName",
          "partName_id"
        );
      }
      modifyFieldInMeta(meta, labelToKeyMap["New Part Name"], {
        options: partNameOptions,
      });
    }

    // see if complaint title is changed
    if (changedValues[labelToKeyMap["New Part Name"]]) {
      // reset description, partename and action taken
      ["New Action Taken"].map((singelFieldLabel) => {
        manipulatedFieldValues[labelToKeyMap[singelFieldLabel]] = "";
      });
      // hide everything after description
      hideEverythingOnTheFormExcept(meta, [
        "New Title",
        "New Description",
        "New Part Name",
        "New Action Taken",
        "Number of line items",
      ]);

      let atmResponseFrActionTaken = await getOptionsFromATMMasterFr({
        group_by: JSON.stringify([
          "ref_item_category_id",
          "ref_item_category",
          "title_id",
          "title",
          "description_id",
          "description",
          "partName_id",
          "partName",
          "action_taken_name",
          "internalId",
        ]),
        where_clause: JSON.stringify({
          ref_item_category_id: refItemCategoryId,
          title_id: allValues[labelToKeyMap["New Title"]],
          description_id: allValues[labelToKeyMap["New Description"]],
          partName_id: allValues[labelToKeyMap["New Part Name"]],
        }),
      });
      let actionTakenOptions = [];
      if (atmResponseFrActionTaken) {
        actionTakenOptions = getFieldOptions(
          atmResponseFrActionTaken,
          "action_taken_name",
          "internalId"
        );
      }
      modifyFieldInMeta(meta, labelToKeyMap["New Action Taken"], {
        options: actionTakenOptions,
      });
    }

    //Fill new Case Category based on selection of all ATM fields and check if action taken is changed
    if (changedValues[labelToKeyMap["New Action Taken"]]) {
      let atmResponseFrCaseCategory = await getOptionsFromATMMasterFr({
        group_by: JSON.stringify([
          "ref_item_category_id",
          "ref_item_category",
          "title_id",
          "title",
          "description_id",
          "description",
          "partName_id",
          "partName",
          "action_taken_name",
          "internalId",
          "ref_comp_category",
          "ref_comp_category_id",
        ]),
        where_clause: JSON.stringify({
          ref_item_category_id: refItemCategoryId,
          title_id: allValues[labelToKeyMap["New Title"]],
          description_id: allValues[labelToKeyMap["New Description"]],
          partName_id: allValues[labelToKeyMap["New Part Name"]],
          internalId: allValues[labelToKeyMap["New Action Taken"]],
        }),
      });
      let caseCategoryOptions = [];
      if (atmResponseFrCaseCategory) {
        caseCategoryOptions = getFieldOptions(
          atmResponseFrCaseCategory,
          "ref_comp_category",
          "ref_comp_category_id"
        );
      }
      manipulatedFieldValues[labelToKeyMap["New Case Category"]] =
        caseCategoryOptions[0]?.value;
      modifyFieldInMeta(meta, labelToKeyMap["New Case Category"], {
        options: caseCategoryOptions,
        disabled: true,
      });
    }
  }

  const numberOfLineItemsOptionsMap = getObjByKeyFrmArray(
    meta,
    "key",
    labelToKeyMap["Number of line items"]
  )?.options;

  const numberOfITemsToSHow =
    getNumberOfItemsToShow(
      allValues,
      numberOfLineItemsOptionsMap,
      labelToKeyMap
    ) || 0;

  //call line item options api

  if (callLineItemOptionAPI(allValues, changedValues, labelToKeyMap)) {
    let data = {
      newTitle: allValues[labelToKeyMap["New Title"]],
      newDescription: allValues[labelToKeyMap["New Description"]],
      newPartName: allValues[labelToKeyMap["New Part Name"]],
      newActionTaken: allValues[labelToKeyMap["New Action Taken"]],
      newCaseCategory: allValues[labelToKeyMap["New Case Category"]],
      tms_case_id: tms_order_id,
    };

    console.log("salesforce api call data", data);

    let netsuiteLineItemOptions = [];
    try {
      const response = await makeRequest(data);
      console.log("salesforce api response", response.data); // Handle the API response data
      if (response.data.status && response.data.message) {
        let { status, items } = response.data;
        if (status == "Success") {
          netsuiteLineItemOptions = [...JSON.parse(items)];
        } else {
          console.log(
            "error response from line item api",
            response.data.message
          );
        }
      } else {
        // this is a fatal error from salesforce
        // the response is not as per documentation rxd
        console.log("Fatal response received from salesforce", response);
      }
    } catch (error) {
      logAxiosError(error);
      // handle error
    }

    // console.log(`labelToKeyMap['Line item 1']`,labelToKeyMap['Line item 1'])
    lineItemOptions = netsuiteLineItemOptions.map((singleNetsuiteOption) => {
      return {
        value: singleNetsuiteOption.itemInternalId,
        label: singleNetsuiteOption.itemCodeAndDisplayName,
        price: singleNetsuiteOption?.itemBasePrice,
      };
    });
    console.log("lineItemOptions ", lineItemOptions);
    lineItemOptions = [...getUniqueEntries(lineItemOptions, "value")];
    console.log("lineItemOptions ", lineItemOptions);
  } else if (lineItemOptionIsLoaded) {
    let firstLineItemFieldMeta = getObjByKeyFrmArray(
      currentMeta,
      "key",
      labelToKeyMap[lineItemsMap[0].item_label]
    );
    console.log("firstLineItemFieldMeta", firstLineItemFieldMeta);
    lineItemOptions = [...firstLineItemFieldMeta?.options];
  }
  modifyFieldInMeta(meta, labelToKeyMap["New Title"], {
    options: titleOptions,
  });

  let lineItemTotal = 0;

  let alreadySelectedItems = [];

  lineItemsMap.map((singleLineItem, i) => {
    // Setting line item options to line item 1
    modifyFieldInMeta(meta, labelToKeyMap[singleLineItem.item_label], {
      options: lineItemOptions,
    });
    if (i < parseInt(numberOfITemsToSHow)) {
      modifyLineItemFields(meta, singleLineItem, labelToKeyMap, {
        hide: false,
        required: true,
      });

      // Setting the price for individual selected line item
      let lineItem =
        allValues[labelToKeyMap[singleLineItem.item_label]] ||
        manipulatedFieldValues[labelToKeyMap[singleLineItem.item_label]];
      // Check if this item was already selected
      if (alreadySelectedItems.includes(lineItem)) {
        // reset this line item field
        lineItem = undefined;
        manipulatedFieldValues[labelToKeyMap[singleLineItem.item_label]] = "";
      } else {
        alreadySelectedItems.push(lineItem);
      }

      if (lineItem) {
        // set price
        let itemPrice = getObjByKeyFrmArray(
          lineItemOptions,
          "value",
          lineItem
        )?.price;

        const currentItemPriceInField =
          allValues[labelToKeyMap[singleLineItem.price_label]];

        if (currentItemPriceInField == undefined || itemPrice) {
          manipulatedFieldValues[labelToKeyMap[singleLineItem.price_label]] =
            itemPrice || 0;
        }

        modifyFieldInMeta(meta, labelToKeyMap[singleLineItem.price_label], {
          disabled: itemPrice ? true : false,
        });
        // Adding to total
        let itemQty = allValues[labelToKeyMap[singleLineItem.qty_label]];

        if (itemQty != null && itemQty <= 0) {
          itemQty = 1;
          manipulatedFieldValues[labelToKeyMap[singleLineItem.qty_label]] = 1;
        }
        if (itemPrice && itemQty) {
          lineItemTotal = lineItemTotal + itemPrice * itemQty;
        }
        // make the qty field not mandatory for item which is not selected
        modifyFieldInMeta(meta, labelToKeyMap[singleLineItem.qty_label], {
          required: true,
        });

        modifyFieldInMeta(meta, labelToKeyMap[singleLineItem.price_label], {
          disabled: true,
        });
      } else {
        // make the qty field not mandatory for item which is not selected
        modifyFieldInMeta(meta, labelToKeyMap[singleLineItem.qty_label], {
          required: false,
        });
        manipulatedFieldValues[labelToKeyMap[singleLineItem.qty_label]] = "";
        manipulatedFieldValues[labelToKeyMap[singleLineItem.price_label]] = "";
      }
    } else {
      modifyLineItemFields(meta, singleLineItem, labelToKeyMap, {
        hide: true,
        // required : true
      });
    }
  });

  //setting service charges and service type
  manipulatedFieldValues[labelToKeyMap["Service Type"]] = serviceType;
  manipulatedFieldValues[labelToKeyMap["Service Charge"]] = serviceCharge;
  modifyFieldInMeta(meta, labelToKeyMap["Service Type"], {
    disabled: true,
  });
  modifyFieldInMeta(meta, labelToKeyMap["Service Charge"], {
    disabled: true,
  });

  let serviceCharges =
    allValues[labelToKeyMap["Service Charges"]] || serviceCharge;
  if (serviceCharges > 0) {
    lineItemTotal = lineItemTotal + serviceCharges;
  }

  manipulatedFieldValues[labelToKeyMap["Total Amount"]] = lineItemTotal;
  modifyFieldInMeta(meta, labelToKeyMap["Total Amount"], {
    disabled: true,
  });

  //hide I/O voltage fields
  const descriptionOptions =
    meta.find((item) => item.label === "New Description")?.options || [];
  if (
    descriptionOptions.find(
      (item) => item.value === allValues[labelToKeyMap["New Description"]]
    )?.label != "Device not working_premium"
  ) {
    console.log("running not hide I/O voltage logic");
    hideMultipleFields(meta, labelToKeyMap, [
      "Input Voltage",
      "Output Voltage",
    ]);
  }

  if (refItemCategory?.includes("Classic 23Litres")) {
    hideMultipleFields(meta, labelToKeyMap, [
      "Water pressure before CSF",
      "Water pressure after CSF",
      "RO Pump pressure",
      "Rejection flow rate",
      "Tank Filling Time",
      "ESF Refittment",
      "Input Water TDS",
      "Output Water TDS",
      "Hardness of input water",
      "Iron level of input water",
    ]);
  }

  modifyFieldInMeta(meta, labelToKeyMap["New Case Category"], {
    disabled: true,
  });

  let disableFormSubmissionButton = false;
  let disableSubmissionButtonField = allValues[labelToKeyMap["Disable Submit"]];
  console.log(
    "disableSubmissionButtonField ---->",
    disableSubmissionButtonField
  );
  if (!disableSubmissionButtonField) {
    disableFormSubmissionButton = true;
  }
  console.log("disableFormSubmission", disableFormSubmissionButton);

  responseStatus = true;
  responseMessage = "success";
  const response = {
    status: responseStatus,
    message: responseMessage,
    data: {
      meta,
      allValues,
      changedValues,
      manipulatedFieldValues,
      disableFormSubmissionButton,
    },
  };
  console.log("final response", response);
  return response;
};

// handler({});

exports.handler = handler;
