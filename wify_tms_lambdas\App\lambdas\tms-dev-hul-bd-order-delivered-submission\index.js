const axios = require("axios");

const filesPrefixUrl = "https://tms-hul-media-demo.wify.co.in/";

const logAxiosError = (error) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.log(error.response.data);
    console.log(error.response.status);
    console.log(error.response.headers);
  } else if (error.request) {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    console.log(error.request);
  } else {
    // Something happened in setting up the request that triggered an Error
    console.log("Error", error.message);
  }
  // console.log(error.config);
};

// Salesforce API request with Bearer token
const makeRequest = async (data) => {
  const salesforceUrl =
    "https://aosmithpureitq--aosqas001.sandbox.my.salesforce.com/services/apexrest/TMSBangladeshOrderDelivered";
  const bearerToken =
    "00Ddm000005NsT3!AQEAQE2qwaRJRIUKvQlmkWpIji.oIcv9Q2UBktb35r6iijb7t4cmJLVpyB87fVkAENFbCdCc3h70dEGDpDZfVvENTQ65HuNy";

  const headers = {
    Authorization: `Bearer ${bearerToken}`,
    "Content-Type": "application/json",
    Cookie:
      "BrowserId=6cQ6rd1TEe-u_MM-5GGxMQ; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1",
  };

  return await axios.post(salesforceUrl, data, { headers });
};

const convertAttachmentToPublicUrl = (internalUrl) => {
  if (internalUrl) {
    const string = internalUrl; //"org_14/capture_image_1124406008470300_uaJ1IeWPIghOVE60.png";
    return `${filesPrefixUrl}${string}`;
  } else {
    return "";
  }
};

const handler = async (event) => {
  let responseStatus = false;
  let responseMessage = "Error please contact admin";

  console.log("event form_data", event.form_data);
  console.log("event request_data", event.request_data);

  let lineItemsMap = [
    {
      item_label: "Item 1",
      qty_label: "Quantity 1",
      price_label: "Price 1",
    },
    {
      item_label: "Item 2",
      qty_label: "Quantity 2",
      price_label: "Price 2",
    },
    {
      item_label: "Item 3",
      qty_label: "Quantity 3",
      price_label: "Price 3",
    },
    {
      item_label: "Item 4",
      qty_label: "Quantity 4",
      price_label: "Price 4",
    },
    {
      item_label: "Item 5",
      qty_label: "Quantity 5",
      price_label: "Price 5",
    },
  ];

  const fieldVsKey = {
    tms_order_id: "tms_display_code",
    "Item 1": "a9be4eaa-7e9b-47f9-b590-b434bffc6590",
    "Quantity 1": "9afefbf4-9307-414c-afd8-731ad94eaec8",
    "Price 1": "400b86d1-fb20-4a1f-85bf-d53456c63c97",
    "Item 2": "c2547033-80a8-4610-b50a-66167e66d936",
    "Quantity 2": "5de6a1ac-6fbf-4624-a4db-bd661c1e58a7",
    "Price 2": "dff1b295-9e70-4946-91e4-0de49e2a0da6",
    "Item 3": "501d02de-2939-461b-ac83-12774d075f3d",
    "Quantity 3": "bc513b9a-9ee4-468d-a542-fcfa0f0381f1",
    "Price 3": "58af1e99-7666-45ff-b884-62742e122440",
    "Item 4": "fe0c0ea7-da2d-4ff5-be24-06762c76581d",
    "Quantity 4": "c668f630-8e4d-4003-b0ec-c677d21b6ee4",
    "Price 4": "6a5f877e-3b4a-41af-a0c1-e63723dd9da7",
    "Item 5": "9a1beaa4-dfb6-427a-b964-875afdbf7b7a",
    "Quantity 5": "ebb333f3-90fb-4ead-887d-653974fab384",
    "Price 5": "5a4850a0-8de3-4f9c-bfd3-e293f30058c6",
    "Service Type": "c2f98c8a-89c5-46c8-bc87-5c1436629150",
    "Delivery Charge": "46712c72-593c-4945-a052-b5e895c77823",
    Total: "e9c2b3d1-6e79-4257-a1b7-24b83e110cb8",
    "After Service Image": "da5b3d6f-c152-4407-bc46-2b705895adcc",
    "Invoice Image": "33bfea9c-8d06-47cb-badb-c38e64c5ed04",
  };

  const tmsOrderId = event.request_data[fieldVsKey.tms_order_id];
  const serviceType = event.form_data[fieldVsKey["Service Type"]];
  const deliveryCharge = event.form_data[fieldVsKey["Delivery Charge"]];
  const total = event.form_data[fieldVsKey["Total"]];
  const afterServiceImage = convertAttachmentToPublicUrl(
    event.form_data.attachments[fieldVsKey["After Service Image"]]?.[0]
  );
  const invoiceImage = convertAttachmentToPublicUrl(
    event.form_data.attachments[fieldVsKey["Invoice Image"]]?.[0]
  );
  const attachments = event.form_data.attachments;

  let itemDataFrSalesforce = [];
  lineItemsMap.map((singleLineItemMap) => {
    const item = event.form_data[fieldVsKey[singleLineItemMap.item_label]];
    if (item) {
      itemDataFrSalesforce.push({
        item: item,
        quantity: event.form_data[fieldVsKey[singleLineItemMap.qty_label]],
        price: event.form_data[fieldVsKey[singleLineItemMap.price_label]],
        total: total,
      });
    }
  });

  console.log("itemDataFrSalesforce", itemDataFrSalesforce);

  let data = {
    tms_order_id: tmsOrderId,
    Items_Data: itemDataFrSalesforce,
    service_type: serviceType,
    delivery_charge: deliveryCharge,
    total: total,
    after_service_image: afterServiceImage,
    invoice_image: invoiceImage,
    attachments: attachments,
  };

  console.log("salesforce api call data", data);
  try {
    const response = await makeRequest(data);
    console.log("salesforce api response", response.data); // Handle the API response data

    const salesforceData = response.data.data?.[0] || response.data;

    if (salesforceData && salesforceData.message) {
      let { message, responseCode, status } = salesforceData;
      responseMessage = message;
      console.log(
        "message,responseCode,status : ",
        message,
        responseCode,
        status
      );
      if (status == "Success") {
        responseStatus = true; //
      } else {
        responseMessage = message;
      }
    } else {
      // this is a fatal error from salesforce
      // the response is not as per documentation rxd
      responseMessage = JSON.stringify(response.data);
    }
  } catch (error) {
    logAxiosError(error);
    // handle error
  }

  // TODO implement
  const response = {
    status: responseStatus,
    message: responseMessage,
  };
  return response;
};

// handler({});

exports.handler = handler;
