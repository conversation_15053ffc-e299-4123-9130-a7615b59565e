const axios = require("axios");

const filesPrefixUrl = "https://tms-hul-media-demo.wify.co.in/";

const logAxiosError = (error) => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.log(error.response.data);
    console.log(error.response.status);
    console.log(error.response.headers);
  } else if (error.request) {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    console.log(error.request);
  } else {
    // Something happened in setting up the request that triggered an Error
    console.log("Error", error.message);
  }
  // console.log(error.config);
};

// Salesforce API request with Bearer token
const makeRequest = async (data) => {
  const salesforceUrl =
    "https://aosmithpureitq--aosqas001.sandbox.my.salesforce.com/services/apexrest/deviceItemOptions";
  const bearerToken =
    "00Ddm000005NsT3!AQEAQE2qwaRJRIUKvQlmkWpIji.oIcv9Q2UBktb35r6iijb7t4cmJLVpyB87fVkAENFbCdCc3h70dEGDpDZfVvENTQ65HuNy";
  const headers = {
    Authorization: `Bearer ${bearerToken}`,
    "Content-Type": "application/json",
    Cookie:
      "BrowserId=6cQ6rd1TEe-u_MM-5GGxMQ; CookieConsentPolicy=0:1; LSKey-c$CookieConsentPolicy=0:1",
  };

  return await axios.post(salesforceUrl, data, { headers });
};

const convertAttachmentToPublicUrl = (internalUrl) => {
  if (internalUrl) {
    const string = internalUrl; //"org_14/capture_image_1124406008470300_uaJ1IeWPIghOVE60.png";
    return `${filesPrefixUrl}${string}`;
  } else {
    return "";
  }
};

const modifyFieldInMeta = (meta, fieldKey, newKeyValueObj) => {
  meta.map((singleField, index) => {
    if (singleField.key == fieldKey) {
      singleField = {
        ...singleField,
        ...newKeyValueObj,
      };
      meta[index] = singleField;
    }
  });
};

const getObjByKeyFrmArray = (array = [], key, value) => {
  return array.filter((singleObj) => singleObj[key] == value)?.[0];
};

function getUniqueEntries(array, keyName) {
  const uniqueEntries = [];
  const uniqueKeys = {};

  for (let i = 0; i < array.length; i++) {
    const obj = array[i];
    const key = obj[keyName];

    if (!uniqueKeys[key]) {
      // Add entry to the uniqueEntries array
      uniqueEntries.push(obj);
      // Add key to the uniqueKeys object
      uniqueKeys[key] = true;
    }
  }

  return uniqueEntries;
}

function getNumberOfItemsToShow(
  allValues,
  numberOfLineItemsOptionsMap,
  labelToKeyMap
) {
  const selectedRadio = allValues[labelToKeyMap["Number of line items"]];
  const radioOption = getObjByKeyFrmArray(
    numberOfLineItemsOptionsMap,
    "value",
    selectedRadio
  );
  return radioOption?.label;
}

function modifyLineItemFields(
  meta,
  lineItemDetails,
  labelToKeyMap,
  updateMeta
) {
  Object.keys(lineItemDetails).map((singleLabel) => {
    modifyFieldInMeta(
      meta,
      labelToKeyMap[lineItemDetails[singleLabel]],
      updateMeta
    );
  });
}

function allValuesIsInitial(allValues) {
  const emptyAllValues = {
    attachments: {},
    mic_files: {},
    camera_files: {},
  };
  return JSON.stringify(allValues) == JSON.stringify(emptyAllValues);
}

function setLineItemsFrmReqData(
  manipulatedFieldValues,
  prefillLineItems,
  lineItemsMap,
  labelToKeyMap,
  meta
) {
  for (let i = 0; i < prefillLineItems.length; i++) {
    manipulatedFieldValues[labelToKeyMap[lineItemsMap[i].item_label]] =
      prefillLineItems[i].InternalId;
    modifyLineItemFields(meta, lineItemsMap[i], labelToKeyMap, {
      disabled: true,
    });
    manipulatedFieldValues[labelToKeyMap[lineItemsMap[i].qty_label]] =
      prefillLineItems[i].quantity;
    manipulatedFieldValues[labelToKeyMap[lineItemsMap[i].price_label]] =
      prefillLineItems[i].amount;
  }
}

function setNumberOfLineItems(
  numberOfLineItemsOptionsMap,
  key,
  numberOfITemsToSHow,
  manipulatedFieldValues,
  labelToKeyMap
) {
  let numberOfPrefillLineItem = getObjByKeyFrmArray(
    numberOfLineItemsOptionsMap,
    key,
    numberOfITemsToSHow
  )?.value;
  manipulatedFieldValues[labelToKeyMap["Number of line items"]] =
    numberOfPrefillLineItem;
}

function setOptionsFrNumberOfLineItems(
  numberOfLineItemsOptionsMap,
  reqLineItemLength,
  meta,
  labelToKeyMap
) {
  const filteredOptions = numberOfLineItemsOptionsMap.filter(
    (singleEntry) => parseInt(singleEntry.label) >= reqLineItemLength
  );
  modifyFieldInMeta(meta, labelToKeyMap["Number of line items"], {
    options: filteredOptions,
  });
}

const checkIfLineItemOptionsIsNotLoaded = (meta, key, labelToKeyMap) => {
  let options = getObjByKeyFrmArray(meta, key, "Item 1").options;
  const lineItemOptionNotLoaded =
    options.length === 2 &&
    options.some((item) => item.label === "Option1") &&
    options.some((item) => item.label === "Option2");
  console.log("checkIfLineItemOptionsIsNotLoaded -->", lineItemOptionNotLoaded);
  return lineItemOptionNotLoaded;
};

const handler = async (event) => {
  let responseStatus = false;
  let responseMessage = "No response received from Netsuite 1";

  const { meta, allValues, changedValues, request_data, currentMeta } = event;

  console.log("event", event);

  const labelToKeyMap = {};

  meta.map((singleField) => {
    labelToKeyMap[singleField.label] = singleField.key;
  });

  console.log("event request_data", event.request_data);

  const fieldVsKey = {
    delivery_charge: "88d57457-8744-449e-be2f-943509fd747f",
    service_type: "ee7a3290-ffd1-4581-ab72-d1daa69f5994",
  };

  let lineItemsMap = [
    {
      item_label: "Item 1",
      qty_label: "Quantity 1",
      price_label: "Price 1",
    },
    {
      item_label: "Item 2",
      qty_label: "Quantity 2",
      price_label: "Price 2",
    },
    {
      item_label: "Item 3",
      qty_label: "Quantity 3",
      price_label: "Price 3",
    },
    {
      item_label: "Item 4",
      qty_label: "Quantity 4",
      price_label: "Price 4",
    },
    {
      item_label: "Item 5",
      qty_label: "Quantity 5",
      price_label: "Price 5",
    },
  ];

  let lineItemOptions = [];

  let isFirstTime =
    Object.keys(changedValues).length == 0 && allValuesIsInitial(allValues);

  let lineItemOptionNotLoaded = checkIfLineItemOptionsIsNotLoaded(
    meta,
    "label",
    labelToKeyMap
  );

  const deliveryCharge = request_data[fieldVsKey.delivery_charge];

  const serviceType = request_data[fieldVsKey.service_type];

  if (isFirstTime || lineItemOptionNotLoaded) {
    // Get the data from API
    const tms_order_id = request_data["tms_display_code"];

    let data = {
      tms_order_id: tms_order_id,
    };

    console.log("salesforce api call data", data);

    let netsuiteLineItemOptions = [];
    try {
      const response = await makeRequest(data);
      console.log("salesforce api response", response.data); // Handle the API response data
      if (response.data.status && response.data.message) {
        let { status, items } = response.data;
        if (status == "Success") {
          netsuiteLineItemOptions = [...JSON.parse(items)];
        } else {
          console.log(
            "error response from line item api",
            response.data.message
          );
        }
      } else {
        // this is a fatal error from netsuite
        // the response is not as per documentation rxd
        console.log("Fatal response received from salesforce", response);
      }
    } catch (error) {
      logAxiosError(error);
      // handle error
    }

    const uniqueKeys = {};
    // console.log(`labelToKeyMap['Line item 1']`,labelToKeyMap['Line item 1'])
    lineItemOptions = netsuiteLineItemOptions.map((singleNetsuiteOption) => {
      return {
        value: singleNetsuiteOption.item_internal_id,
        label: singleNetsuiteOption.item_code_and_display_name,
        price: singleNetsuiteOption?.item_base_price,
      };
    });
    console.log("lineItemOptions ", lineItemOptions);
    lineItemOptions = [...getUniqueEntries(lineItemOptions, "value")];
    console.log("lineItemOptions ", lineItemOptions);
  } else {
    let firstLineItemFieldMeta = getObjByKeyFrmArray(
      currentMeta,
      "key",
      labelToKeyMap[lineItemsMap[0].item_label]
    );
    console.log("firstLineItemFieldMeta", firstLineItemFieldMeta);
    lineItemOptions = [...firstLineItemFieldMeta.options];
  }

  const manipulatedFieldValues = {};

  let lineItemTotal = 0;
  let prefillLineItems = request_data?.line_items_list || [];
  console.log("prefillLineItems ", prefillLineItems);
  //autofill fields based on request data and modify form
  const numberOfLineItemsOptionsMap = getObjByKeyFrmArray(
    meta,
    "key",
    labelToKeyMap["Number of line items"]
  ).options;

  const numberOfITemsToSHow =
    getNumberOfItemsToShow(
      allValues,
      numberOfLineItemsOptionsMap,
      labelToKeyMap
    ) || prefillLineItems.length;

  let alreadySelectedItems = [];
  setLineItemsFrmReqData(
    manipulatedFieldValues,
    prefillLineItems,
    lineItemsMap,
    labelToKeyMap,
    meta
  );
  lineItemsMap.map((singleLineItem, i) => {
    if (i < parseInt(numberOfITemsToSHow)) {
      modifyLineItemFields(meta, singleLineItem, labelToKeyMap, {
        hide: false,
        // required : true
      });
      // Setting line item options to line item 1
      modifyFieldInMeta(meta, labelToKeyMap[singleLineItem.item_label], {
        options: lineItemOptions,
      });
      // Setting the price for individual selected line item
      let lineItem =
        allValues[labelToKeyMap[singleLineItem.item_label]] ||
        manipulatedFieldValues[labelToKeyMap[singleLineItem.item_label]];
      // Check if this item was already selected
      if (alreadySelectedItems.includes(lineItem)) {
        // reset this line item field
        lineItem = undefined;
        manipulatedFieldValues[labelToKeyMap[singleLineItem.item_label]] = "";
      } else {
        alreadySelectedItems.push(lineItem);
      }

      if (lineItem) {
        // set price
        let itemPrice = getObjByKeyFrmArray(
          lineItemOptions,
          "value",
          lineItem
        )?.price;

        const currentItemPriceInField =
          allValues[labelToKeyMap[singleLineItem.price_label]];

        if (currentItemPriceInField == undefined || itemPrice) {
          manipulatedFieldValues[labelToKeyMap[singleLineItem.price_label]] =
            itemPrice || 0;
        }

        modifyFieldInMeta(meta, labelToKeyMap[singleLineItem.price_label], {
          disabled: itemPrice ? true : false,
        });
        // Adding to total
        let itemQty =
          allValues[labelToKeyMap[singleLineItem.qty_label]] ||
          manipulatedFieldValues[labelToKeyMap[singleLineItem.qty_label]];

        if (itemQty != null && itemQty <= 0) {
          itemQty = 1;
          manipulatedFieldValues[labelToKeyMap[singleLineItem.qty_label]] = 1;
        }
        if (itemPrice && itemQty) {
          lineItemTotal = lineItemTotal + itemPrice * itemQty;
        }
        // make the qty field not mandatory for item which is not selected
        modifyFieldInMeta(meta, labelToKeyMap[singleLineItem.qty_label], {
          required: true,
        });

        modifyFieldInMeta(meta, labelToKeyMap[singleLineItem.price_label], {
          disabled: true,
        });
      } else {
        // make the qty field not mandatory for item which is not selected
        modifyFieldInMeta(meta, labelToKeyMap[singleLineItem.qty_label], {
          required: false,
        });
        manipulatedFieldValues[labelToKeyMap[singleLineItem.qty_label]] = "";
        manipulatedFieldValues[labelToKeyMap[singleLineItem.price_label]] = "";
      }
    } else {
      modifyLineItemFields(meta, singleLineItem, labelToKeyMap, {
        hide: true,
        // required : true
      });
    }
  });

  //setting inital number of line items to show and hiding smaller options
  setNumberOfLineItems(
    numberOfLineItemsOptionsMap,
    "label",
    numberOfITemsToSHow,
    manipulatedFieldValues,
    labelToKeyMap
  );
  setOptionsFrNumberOfLineItems(
    numberOfLineItemsOptionsMap,
    prefillLineItems.length,
    meta,
    labelToKeyMap
  );

  //setting delivery charges and service type
  manipulatedFieldValues[labelToKeyMap["Service Type"]] = serviceType;
  manipulatedFieldValues[labelToKeyMap["Delivery Charge"]] = deliveryCharge;
  modifyFieldInMeta(meta, labelToKeyMap["Service Type"], {
    disabled: true,
  });
  modifyFieldInMeta(meta, labelToKeyMap["Delivery Charge"], {
    disabled: true,
  });

  let deliveryCharges =
    allValues[labelToKeyMap["Delivery Charges"]] || deliveryCharge;
  if (deliveryCharges > 0) {
    lineItemTotal = lineItemTotal + deliveryCharges;
  }

  manipulatedFieldValues[labelToKeyMap["Total"]] = lineItemTotal;
  modifyFieldInMeta(meta, labelToKeyMap["Total"], {
    disabled: true,
  });

  responseStatus = true;
  responseMessage = "success";
  const response = {
    status: responseStatus,
    message: responseMessage,
    data: { meta, allValues, changedValues, manipulatedFieldValues },
  };
  return response;
};

// handler({});

exports.handler = handler;
